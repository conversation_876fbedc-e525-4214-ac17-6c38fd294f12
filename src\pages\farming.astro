---
export const prerender = false;
import { db, ItemTbl, TypeTbl } from "astro:db";

if (Astro.request.method === "POST") {
	const formData = await Astro.request.formData();
	console.log(formData);
	await db.insert(ItemTbl).values({
		itemName: formData.get("itemName"),
		type: formData.get("type"),
		buyPrice: formData.get("buyPrice"),
		sellPrice: formData.get("sellPrice"),
	});
}

const itemTypes = await db.select().from(TypeTbl);
const items = await db.select().from(ItemTbl);
---

<h1>Farming add stuff</h1>
<form id='itemForm' method='POST' style='display: flex; flex-direction: column;'>
	<label for='itemName'>Name</label>
	<input type='text' name='itemName' id='itemName' />
	<label for='itemType'>Type</label>
	<select name='type' id='itemType'>
		{itemTypes.map((itemType) => <option value={itemType.id}>{itemType.name}</option>)}
	</select>
	<label for='buyPrice'>Buy price</label>
	<input type='number' name='buyPrice' id='buyPrice' />
	<label for='sellPrice'>Sell price</label>
	<input type='number' name='sellPrice' id='sellPrice' />
	<button type='submit'>Add</button>
</form>

<div>
	<div>Items</div>
	{
		items?.map((item) => {
			return (
				<div>
					<pre>{JSON.stringify(item, null, 2)}</pre>
				</div>
			);
		})
	}
</div>
