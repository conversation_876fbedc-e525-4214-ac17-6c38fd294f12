---
export const prerender = false;
import astroLogo from "../assets/astro.svg";
import background from "../assets/background.svg";
import { db, Comment, Author, eq } from "astro:db";

// Handle POST request from the form submission
if (Astro.request.method === "POST") {
	// 1. Get the form data from the request
	const formData = await Astro.request.formData();
	// // 2. Extract and validate the data
	const authorIdString = formData.get("author");
	const body = formData.get("body");
	if (typeof authorIdString === "string" && typeof body === "string") {
		const parsedAuthorId = parseInt(authorIdString, 10);
		// 3. Insert into the database if the data is valid
		if (!isNaN(parsedAuthorId)) {
			await db.insert(Comment).values({ authorId: parsedAuthorId, body });
			console.log("New comment added to the database.");
		}
	}
	// 4. Return a response to the client-side fetch call
	// return new Response(null, { status: 200 });
}

const comments = await db.select().from(Comment).innerJoin(Author, eq(Comment.authorId, Author.id));
---

<div id='container'>
	<div>
		<form id='comment-form' method='POST' style='display: grid'>
			<label for='author'>Author</label>
			<input type='number' id='author' name='author' />

			<label for='body'>Body</label>
			<textarea id='body' name='body'></textarea>

			<button type='submit'>Submit</button>
		</form>
	</div>

	<div>
		<div>Comments</div>
		{
			comments.map(({ Author, Comment }) => (
				<div>
					<div>{Author.name}</div>
					<div>{Comment.body}</div>
				</div>
			))
		}
	</div>
</div>
