import { defineDb, defineTable, column } from 'astro:db';

const Comment = defineTable({
  columns: {
    authorId: column.number({ references: () => Author.columns.id }),
    body: column.text(),
  }
});

const Author = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    name: column.text(),
  }
});


const TypeTbl = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    name: column.text(),
  }
});

const ItemTbl = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    itemName: column.text(),
    type: column.number({ references: () => TypeTbl.columns.id }),
    buyPrice: column.number(),
    sellPrice: column.number(),
  }
});

const SeasonTbl = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    name: column.text(),
  }
});

const CropTbl = defineTable({
  columns: {
    itemId: column.number({ references: () => ItemTbl.columns.id, primaryKey: true }),
    growTime: column.number(),
    regrowTime: column.number({ optional: true }),
  }
});

const CropSeasonTbl = defineTable({
  columns: {
    cropId: column.number({ references: () => CropTbl.columns.itemId }),
    seasonId: column.number({ references: () => SeasonTbl.columns.id }),
  },
  indexes: [
    {
      on: ['cropId', 'seasonId'], unique: true
    }
  ]
});

//the recipie table 
const RecipeTbl = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    toolId: column.number({ references: () => ItemTbl.columns.id }),
  }
});

//input for a recipe
const RecipeIngredientTbl = defineTable({
  columns: {
    recipeId: column.number({ references: () => RecipeTbl.columns.id }),
    itemId: column.number({ references: () => ItemTbl.columns.id }),
    quantity: column.number(),
  },
  indexes: [
    {
      on: ['recipeId', 'itemId'], unique: true
    }
  ]
});

//output for a recipe
const RecipeOutputTbl = defineTable({
  columns: {
    recipeId: column.number({ references: () => RecipeTbl.columns.id }),
    itemId: column.number({ references: () => ItemTbl.columns.id }),
    quantity: column.number(),
  },
  indexes: [
    {
      on: ['recipeId', 'itemId'], unique: true
    }
  ]
});

//after calculated profit for item (output - input) sellPrice
//also has information on the season it will be avaible in
const ProfitTbl = defineTable({
  columns: {
    itemId: column.number({ references: () => ItemTbl.columns.id }),
    seasonId: column.number({ references: () => SeasonTbl.columns.id }),
    profit: column.number(),
    profit28Days: column.number(),
    profitPerDay: column.number(),
  },
  indexes: [
    {
      on: ['itemId', 'seasonId'], unique: true
    }
  ]
});


// https://astro.build/db/config
export default defineDb({
  tables: {
    Comment, Author,
    ItemTbl,
    TypeTbl,
    SeasonTbl,
    CropTbl,
    CropSeasonTbl,
    RecipeTbl,
    RecipeIngredientTbl,
    RecipeOutputTbl,
    ProfitTbl,
  }
});
