import { db, Comment, Author, TypeTbl, SeasonTbl, ItemTbl, CropTbl, CropSeasonTbl, ProfitTbl } from 'astro:db';

// https://astro.build/db/seed
export default async function seed() {
  // TODO
  await db.insert(Author).values([
    { id: 1, name: "<PERSON><PERSON><PERSON>" },
    { id: 2, name: "<PERSON>" },
  ]);

  await db.insert(Comment).values([
    { authorId: 1, body: 'Hope you like Astro DB!' },
    { authorId: 2, body: 'Enjoy!' },
  ]);

  await db.insert(TypeTbl).values([
    { name: '<PERSON><PERSON>' },
    { name: 'Ingredient' },
    { name: 'Tool' },
  ]);


  // Insert seasons, including the new 'Spring' season
  await db.insert(SeasonTbl).values([
    { id: 1, name: 'spring' },
    { id: 2, name: 'summer' },
    { id: 3, name: 'autumn' },
    { id: 4, name: 'winter' },
    { id: 5, name: 'Any' },
  ]);

  // Insert items, including the new 'Blue Jazz'
  await db.insert(ItemTbl).values([
    { id: 1, itemName: 'Blue Jazz', type: 1, buyPrice: 30, sellPrice: 50 },
  ]);

  // Insert crop-specific data, including the new 'Blue Jazz'
  await db.insert(CropTbl).values([
    // New crop entry: Blue Jazz
    { itemId: 1, growTime: 7, regrowTime: null },
  ]);

  await db.insert(CropSeasonTbl).values([
    { cropId: 5, seasonId: 1 }, // Blue Jazz (also grows in Spring)
  ]);

  await db.insert(ProfitTbl).values([
    { itemId: 5, seasonId: 1, profit: 20, profit28Days: 20 * 7 }, // Blue Jazz profit = sellPrice (50) - buyPrice (30)
  ]);


}
